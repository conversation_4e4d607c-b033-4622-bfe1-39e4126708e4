---
sidebar_position: 50
title: Related Projects
---


![Docker Image](/img/laradock/laradock-abstract.jpg)

## Laradock Related Projects

* [Docker Stacks](https://github.com/sfx101/docker-stacks): A GUI for managing Laradock. (by [<PERSON><PERSON><PERSON>](https://github.com/sfx101))
* [Laradock CLI](https://github.com/lorinlee/laradock-cli): A CLI for managing Laradock. (by [<PERSON><PERSON>](https://github.com/lorinlee))
* [<PERSON>dock CLI](https://github.com/loonpwn/laradock-cli): A CLI for managing Laradock. (by [<PERSON>](https://github.com/loonpwn))
* [Ansible Laradock Kubernetes](https://github.com/sifat-rahim/ansible-laradock-kubernetes): Ansible playbook to setup docker containers for Laravel apps using Laradock. (by [<PERSON><PERSON><PERSON>](https://github.com/sifat-rahim))
* [<PERSON>](https://github.com/zeroc0d3/monitor-laradock): Laradock Monitoring Tools (using Grafana). (by [Dwi <PERSON>ahn<PERSON>ni](https://github.com/zeroc0d3))
* [Laradock Manager](https://github.com/Lyimmi/laradock-manager): A simple app for managing Laradock containers. Made with wails.app (go & vue.js & vuetify). (by [Zámbó Levente](https://github.com/Lyimmi))
* [Laradock Env](https://github.com/bagart/laradock_env): A wrapper with commands for managing Laradock. (by [BAG Art](https://github.com/bagart))
* [Lara Query](https://github.com/TanisukeGoro/laraQuery): Easy Laradock CLI. (by [Okita kamegoro](https://github.com/TanisukeGoro))
* [Laradock CLI](https://github.com/tonysm/laradock-cli): Laradock CLI helper. (by [Tony Messias](https://github.com/Tonysm))
* [Laradock Lite](https://github.com/yangliuyu/laradock-lite): A Docker based laravel development environment with minimal dependencies. (by [Liu Yang](https://github.com/yangliuyu))
* [Laradock Makefile](https://github.com/bazavlukd/laradock-makefile): Makefile with some useful commands for Laradock. (by [Dmitry Bazavluk](https://github.com/bazavlukd))
* [Laradock Build](https://github.com/dockerframework/laradock-build): Docker builder & running script for Laradock. (by [Docker Framework](https://github.com/dockerframework))
* [Laravel Laradock PHPStorm](https://github.com/LarryEitel/laravel-laradock-phpstorm): Guide for configuring PHPStorm for remote debugging with Laravel & Laradock. (by [Larry Eitel](https://github.com/LarryEitel))
* [Laradock Crudbooster](https://github.com/nutellinoit/laradock-crudbooster): Docker compose & Kubernetes solution to build apps with crudbooster & Laradock. (by [Samuele Chiocca](https://github.com/nutellinoit))
* [Laradock Sample](https://github.com/tadaken3/laradock-sample): Install Laravel with Laradock. (by [Tadaken3](https://github.com/tadaken3))
* [Stylemix's Laradock](https://github.com/stylemix/laradock): Alternate laradock for multiproject purpose. (by [Stylemix LLC](https://github.com/stylemix))




## Inspired by Laradock

* [Dockery](https://github.com/taufek/dockery): Laradock for Ruby. (by [Taufek Johar](https://github.com/Taufek))
* [RubyDev Dock](https://github.com/scudelletti/rubydev-dock): Laradock for Ruby. (by [Diogo Scudelletti](https://github.com/scudelletti))
* [NoDock](https://github.com/Osedea/nodock): Laradock for NodeJS. (by [Osedea](https://github.com/Osedea))
* [Laradock Multi](https://github.com/bagart/laradock-multi): Laradock for PHP & NodeJS. (by [BAG Art](https://github.com/bagart))
* [Wordpress Laradock](https://github.com/shov/wordpress-laradock): Laradock for Wordpress. (by [Alexandr Shevchenko](https://github.com/shov))
* [Yii2 Laradock](https://github.com/ydatech/yii2-laradock): Laradock for Yii2. (by [Yuda Sukmana](https://github.com/ydatech))
* [MageDock](https://github.com/ojhaujjwal/magedock): Laradock for Magento. (by [Ujjwal Ojha](https://github.com/ojhaujjwal))
* [Docker Codeigniter](https://github.com/sebastianlzy/docker-codeigniter): Laradock for Codeigniter. (by [Sebastian](https://github.com/sebastianlzy))
* [Klaradock](https://github.com/poyhsiao/Klaradock): A customized Laradock. (by [Kim Hsiao](https://github.com/poyhsiao))
* [Laravel Boilerplate](https://github.com/casivaagustin/laravel-boilerplate): A boilerplate with support for JWT. (by [Casiva Agustin](https://github.com/casivaagustin))





<br/><br/>

> Feel free to submit a PR for listing your project here.
