<VirtualHost *:80>
    ServerName laradock.test
    ServerAlias *.laradock.test

    RewriteEngine On
    RewriteCond %{HTTPS} !on
    RewriteRule (.*) https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]
</VirtualHost>

<VirtualHost *:443>
    ServerName laradock.test
    ServerAlias *.laradock.test
    DocumentRoot /var/www/
    Options Indexes FollowSymLinks

    SSLEngine on
    SSLCertificateFile /etc/apache2/ssl/ssl_site.crt
    SSLCertificateKeyFile /etc/apache2/ssl/ssl_site.key

    <Directory "/var/www/">
      AllowOverride All
      <IfVersion < 2.4>
        Allow from all
      </IfVersion>
      <IfVersion >= 2.4>
        Require all granted
      </IfVersion>
    </Directory>

    ErrorLog /var/log/apache2/error.log
    CustomLog /var/log/apache2/access.log combined
</VirtualHost>
