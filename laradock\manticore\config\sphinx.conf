index testrt {
    type = rt
    rt_mem_limit = 128M
    path = /var/lib/manticore/data/testrt
    rt_field = title
    rt_field = content
    rt_attr_uint = gid
}

searchd {
    listen = 9312
    listen = 9308:http
    listen = 9306:mysql41
    log = /var/log/manticore/searchd.log
    # you can also send query_log to /dev/stdout to be shown in docker logs
    query_log = /var/log/manticore/query.log
    read_timeout = 5
    max_children = 30
    pid_file = /var/run/manticore/searchd.pid
    seamless_rotate = 1
    preopen_indexes = 1
    unlink_old = 1
    binlog_path = /var/lib/manticore/data
}

