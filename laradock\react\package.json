{"name": "react-redux-boilerplate", "version": "0.1.0", "private": true, "dependencies": {"@material-ui/core": "^4.11.0", "@material-ui/icons": "^4.9.1", "@material-ui/lab": "^4.0.0-alpha.56", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "axios": "^1.6.0", "bootstrap": "^4.5.3", "jss-rtl": "^0.3.0", "node-sass": "^9.0.0", "prop-types": "^15.7.2", "react": "^17.0.1", "react-bootstrap": "^1.4.0", "react-dom": "^17.0.1", "react-intl": "^4.7.6", "react-redux": "^7.2.2", "react-router-dom": "^5.2.0", "react-scripts": "5.0.1", "redux": "^4.0.5", "redux-saga": "^1.1.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}